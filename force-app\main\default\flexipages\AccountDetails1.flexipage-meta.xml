<?xml version="1.0" encoding="UTF-8"?>
<FlexiPage xmlns="http://soap.sforce.com/2006/04/metadata">
    <flexiPageRegions>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>decorate</name>
                    <value>true</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>richTextValue</name>
                    <value>&lt;p&gt;&lt;strong&gt;Dettaglio Anagrafica&lt;/strong&gt;&lt;/p&gt;</value>
                </componentInstanceProperties>
                <componentName>flexipage:richText</componentName>
                <identifier>flexipage_richText</identifier>
            </componentInstance>
        </itemInstances>
        <name>header</name>
        <type>Region</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>flexcardName</name>
                    <value>BSN_AnagraficaDetails</value>
                </componentInstanceProperties>
                <componentName>runtime_omnistudio:flexcard</componentName>
                <identifier>runtime_omnistudio_flexcard2</identifier>
            </componentInstance>
        </itemInstances>
        <name>main</name>
        <type>Region</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>flexcardName</name>
                    <value>DA_Consensi</value>
                </componentInstanceProperties>
                <componentName>runtime_omnistudio:flexcard</componentName>
                <identifier>runtime_omnistudio_flexcard4</identifier>
                <visibilityRule>
                    <criteria>
                        <leftValue>{!Record.ExternalId__c}</leftValue>
                        <operator>CONTAINS</operator>
                        <rightValue>SOC_1</rightValue>
                    </criteria>
                </visibilityRule>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>flexcardName</name>
                    <value>FlexFatcaPG</value>
                </componentInstanceProperties>
                <componentName>runtime_omnistudio:flexcard</componentName>
                <identifier>runtime_omnistudio_flexcard</identifier>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>flexcardName</name>
                    <value>DA_Codice_Attivita</value>
                </componentInstanceProperties>
                <componentName>runtime_omnistudio:flexcard</componentName>
                <identifier>runtime_omnistudio_flexcard3</identifier>
            </componentInstance>
        </itemInstances>
        <name>sidebar</name>
        <type>Region</type>
    </flexiPageRegions>
    <masterLabel>BSN_AccountDetails</masterLabel>
    <sobjectType>AccountDetails__c</sobjectType>
    <template>
        <name>flexipage:recordHomeTemplateDesktop</name>
        <properties>
            <name>enablePageActionConfig</name>
            <value>false</value>
        </properties>
    </template>
    <type>RecordPage</type>
</FlexiPage>
