import { LightningElement, api, track } from 'lwc';
import { ShowToastEvent } from 'lightning/platformShowToastEvent';
import { NavigationMixin } from 'lightning/navigation';
import { registerRefreshContainer, REFRESH_ERROR, REFRESH_COMPLETE, REFRESH_COMPLETE_WITH_ERRORS } from 'lightning/refresh';
import areas_of_need_images from '@salesforce/resourceUrl/areas_of_need';
import getStoredQuotesInfo from '@salesforce/apex/AccordionController.getStoredQuotesInfo';
import retrieveProducts from '@salesforce/apex/STProductTabsetController.retrieveProducts';

// FEI flow constants
const PREVENTIVI_FLOW = 'FEIQuickActionPreventivi';
const FEI_PREVENTIVI_VIEW = {
  ESSIG_AUTO:            'IP.CRUSCOTTO.DISPATCH',
  ESSIG_RE:              'RE.CRUSCOTTO.DISPATCH',
  ESSIG_VITA_PREVIDENZA: 'IP.CRUSCOTTO.DISPATCH'
};
const TITLES_PREVENTIVI_VIEW = {
  ESSIG_AUTO:            'Visualizza preventivo',
  ESSIG_RE:              'Visualizza preventivo',
  ESSIG_VITA_PREVIDENZA: 'Visualizza preventivo'
};
const ESSIG_DOMAINS = new Set(['ESSIG_AUTO','ESSIG_RE','ESSIG_VITA']);
const MIN_SCREEN_SIZE_FOR_PC = 1200;

// Constants for image URLs
const BASE = areas_of_need_images + '/areas_of_need/on';
const PET_IMG        = BASE + '/pet.png';
const HOUSE_IMG      = BASE + '/casa.png';
const FAMILY_IMG     = BASE + '/famiglia.png';
const INJURIES_IMG   = BASE + '/infortuni.png';
const MOTORCYCLE_IMG = BASE + '/mobilita.png';
const HEALTH_IMG     = BASE + '/salute.png';
const CAR_IMG        = BASE + '/veicoli.png';
const TRAVEL_IMG     = BASE + '/viaggi.png';

export default class QuoteAccordionDisplay extends NavigationMixin(LightningElement) {
    @api recordId;
    @api isStored = false;
    @api source;
    @track isLoading = true;
    @track quotes = [];
    @track hasQuote = false;
    @track isTablet = window.innerWidth <= MIN_SCREEN_SIZE_FOR_PC;
     // Flow & preview
  @track showFEI            = false;
  @track showPreview;
  @track flowName           = 'FEIQuickActionUNICA';
  @track flowTitle          = '';
  @track flowInputVariables = [];
  @track currentPreviewUrl     = '';
  @track currentPreviewDomain  = '';
  @track currentQuotes = [];
  @track storedQuotes = [];
  @track hasStoredQuotes = false;
    _refreshContainerId;
  get unicaLabel() {
    const all = this.currentQuotes.length ? this.currentQuotes : this.storedQuotes;
    if (!all.length) {
      return 'UNICA';
    }
    const raw = (all[0].domainType || '').trim().toUpperCase();
    return ESSIG_DOMAINS.has(raw) ? 'ESSIG' : 'UNICA';
  }


    @api
    get targetId() {
        return this.recordId;
    }
    set targetId(val) {
        this.recordId = val;
    }


  
  @track storedOpen = false;
get storedSectionIcon() {
  return this.storedOpen ? 'utility:chevrondown' : 'utility:chevronright';
}
toggleStoredSection() {
  this.storedOpen = !this.storedOpen;
}

    @api
    get quoteStored() {
        return this.isStored;
    }
    set quoteStored(val) {
        this.isStored = val;
    }

    @api
    get productChannel() {
        return this.source;
    }
    set productChannel(val) {
        this.source = val;
    }
  get isPrevidenza() {
    return this.source === 'Preventivatore Previdenza';
  }

async connectedCallback() {
  console.log('✅ recordId ricevuto dal FlexCard in pre:', this.recordId);
  await new Promise(resolve => setTimeout(resolve, 3000));
console.log('✅ recordId ricevuto dal FlexCard:', this.recordId);
  window.addEventListener('resize', this.handleResize);
  this._refreshContainerId = registerRefreshContainer(this, this.refreshContainer);
  
  await this.fetchQuotes();
}



    disconnectedCallback() {
        window.removeEventListener('resize', this.handleResize);
    }

    handleResize = () => {
        this.isTablet = window.innerWidth <= MIN_SCREEN_SIZE_FOR_PC;
    }

   fetchQuotes() {
  this.isLoading = true;

  // 👉 Recupera il primo prodotto collegato
  retrieveProducts({ recordId: this.recordId })
    .then(products => {
      if (!products || !products.length) {
        console.warn('❗ Nessun prodotto trovato per recordId:', this.recordId);
        this.productRecordId = null;
        return;
      }

      this.productRecordId = products[0].id;
      console.log('🧩 Prodotto trovato:', this.productRecordId);

      // Qui parte la tua logica esistente con this.productRecordId al posto di this.recordId
      const pCurrent = getStoredQuotesInfo({
        recordId: this.productRecordId,
        isStored: false
      });

      const pStored = getStoredQuotesInfo({
        recordId: this.productRecordId,
        isStored: true
      });

      return Promise.all([pCurrent, pStored]).then(([dataCurrent, dataStored]) => {
        console.log('📨 current:', dataCurrent);
        console.log('📨 stored:', dataStored);

        this.currentQuotes = dataCurrent.map(qd => this._mapQuote(qd));
        this.storedQuotes  = dataStored .map(qd => this._mapQuote(qd));

        this.hasQuote        = this.currentQuotes.length > 0;
        this.hasStoredQuotes = this.storedQuotes.length  > 0;
      });
    })
    .catch(err => {
      console.error('❌ Errore fetchQuotes:', err);
      this.showToast('Errore', 'Contattare l’amministratore di sistema.', 'error');
    })
    .finally(() => {
      this.isLoading = false;
    });
}

// helper privato che estrae la logica di mapping
_mapQuote(qd) {
  const opportunityCoverages = qd.opportunityCoverages.map((cov, idx) => ({
    key: `${cov.areaOfNeed}-${cov.amount}-${idx}`,
    areaIcon: this.convertAreaOfNeedToImage(cov.areaOfNeed),
    assetItems: cov.areaOfNeed === 'Famiglia' ? ['Famiglia'] : cov.assets,
    descriptionItems: cov.description,
    amount: cov.amount,
    stage: cov.stage,
    fractionation: cov.fractionation,
    conventions: cov.conventions || '-',
    fullName: cov.fullName || `${cov.firstName} ${cov.lastName}`,
    targetProduct: cov.targetProduct,
    ral: cov.ral,
    yearlyGrowth: cov.yearlyGrowth,
    previdentialGap: cov.previdentialGap,
    retirementYear: cov.retirementYear,
    numOfChildren: cov.numOfChildren,
    sector: cov.sector
  }));

  return {
    recordId:          qd.recordId,
    name:              qd.name,
    status:            qd.commercialStatus,
    totalAmount:       qd.totalAmount,
    monthlyContribution: qd.monthlyContribution,
    digitalStep:       qd.digitalStep || '-',
    source:            qd.source,
    cip:               qd.cip,
    creationDate:      qd.creationDate,
    expirationDate:    qd.expirationDate,
    unicaLink:         qd.unicaLink,
    documentUrl:       qd.documentUrl,
    isOpen:            false,
    isStored:          qd.isStored,
    domainType:        qd.domainType,
    areasOfNeedImages: this.convertAreasOfNeedToImages(qd.areasOfNeed),
    opportunityCoverages
  };
}

/** Restituisce l’array di URL immagini per gli ambiti */
convertAreasOfNeedToImages(areas = []) {
  return areas
    .map(a => this.convertAreaOfNeedToImage(a))
    .filter(u => u); // scarta eventuali null
}


convertAreaOfNeedToImage(area) {
  switch ((area||'').trim()) {
    case 'Cane e Gatto': return PET_IMG;
    case 'Casa':         return HOUSE_IMG;
    case 'Famiglia':     return FAMILY_IMG;
    case 'Infortuni':    return INJURIES_IMG;
    case 'Mobilita':     return MOTORCYCLE_IMG;
    case 'Salute':       return HEALTH_IMG;
    case 'Veicoli':      return CAR_IMG;
    case 'Viaggio':      return TRAVEL_IMG;
    default:             return null;
  }
}



    toggleSection(event) {
        const id = event.currentTarget.dataset.id;
        // Aggiorna currentQuotes
        this.currentQuotes = this.currentQuotes.map(q => {
            if (q.recordId === id) {
                return { ...q, isOpen: !q.isOpen };
            }
            return q;
        });
        // Aggiorna storedQuotes
        this.storedQuotes = this.storedQuotes.map(q => {
            if (q.recordId === id) {
                return { ...q, isOpen: !q.isOpen };
            }
            return q;
        });
    }


    handleQuoteClick(event) {
        event.stopPropagation();
        const quoteId = event.currentTarget.dataset.id;
        if (quoteId) {
            this[NavigationMixin.Navigate]({
                type: 'standard__recordPage',
                attributes: { recordId: quoteId, actionName: 'view' }
            });
        }
    }

  handleUnicaClick(event) {
    const recId = event.currentTarget.dataset.id;
    const q = this.quotes.find(x => x.recordId === recId);
    if (q && q.unicaLink) {
      window.open(q.unicaLink, '_blank');
    }
  }

   handlePdfClick(event) {
    const recId = event.currentTarget.dataset.id;
    const q = this.quotes.find(x => x.recordId === recId);
    if (!q) return;

    const dt = (q.domainType || '').trim().toUpperCase();
    if (dt === 'PU' || dt === 'ESSIG_VITA_PREVIDENZA') {
      this.currentPreviewUrl    = q.documentUrl;
      this.currentPreviewDomain = q.domainType;
      this.openPDFPreview();
    } else {
      this.launchPdfFlowForQuote(q);
    }
  }

    openPDFPreview()  { this.showPreview = true; }
  closePDFPreview() { this.showPreview = false; }

  /** Estrae da AccordionHeader.launchPreventivi() solo per il PDF */
 launchPdfFlowForQuote(q) {
    const key  = (q.domainType || '').trim().toUpperCase();
    const feiid = FEI_PREVENTIVI_VIEW[key];
    const title = TITLES_PREVENTIVI_VIEW[key];
    if (!feiid) {
      console.warn(`FEIID non disponibile per ramo "${key}"`);
      return;
    }
    this.flowName = PREVENTIVI_FLOW;
    this.flowTitle = title;
    this.flowInputVariables = [
      { name: 'recordId', type: 'String', value: q.recordId },
      { name: 'FEIID',     type: 'String', value: feiid    }
    ];
    this.showFEI = true;
  }

  // chiude il flow container
  handleClose() {
    this.showFEI = false;
  }


    showToast(title, message, variant) {
        this.dispatchEvent(new ShowToastEvent({ title, message, variant }));
    }

    refreshContainer(refreshPromise) {
        console.log("Refreshing QuoteAccordionDisplay (simplified)");
        this.fetchQuotes();
        return refreshPromise.then(status => {
            if (status === REFRESH_COMPLETE) {
                console.log("Refresh Done!");
            } else if (status === REFRESH_COMPLETE_WITH_ERRORS) {
                console.warn("Refresh Done with issues");
            } else if (status === REFRESH_ERROR) {
                console.error("Refresh Major error");
            }
        });
    }
}