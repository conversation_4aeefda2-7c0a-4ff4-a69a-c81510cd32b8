<?xml version="1.0" encoding="UTF-8"?>
<OmniUiCard xmlns="http://soap.sforce.com/2006/04/metadata">
    <authorName>Unipolsai</authorName>
    <dataSourceConfig>{&quot;dataSource&quot;:{&quot;type&quot;:&quot;IntegrationProcedures&quot;,&quot;value&quot;:{&quot;dsDelay&quot;:&quot;&quot;,&quot;ipMethod&quot;:&quot;LeadManagement_RetrieveProductConfiguration&quot;,&quot;vlocityAsync&quot;:false,&quot;inputMap&quot;:{&quot;recordId&quot;:&quot;{Session.ProductId}&quot;},&quot;jsonMap&quot;:&quot;{\&quot;Session.ProductId\&quot;:\&quot;{Session.ProductId}\&quot;}&quot;,&quot;resultVar&quot;:&quot;&quot;},&quot;orderBy&quot;:{&quot;name&quot;:&quot;&quot;,&quot;isReverse&quot;:&quot;&quot;},&quot;contextVariables&quot;:[{&quot;name&quot;:&quot;Session.ProductId&quot;,&quot;val&quot;:&quot;0069O00000S2clqQAB&quot;,&quot;id&quot;:4}]}}</dataSourceConfig>
    <isActive>true</isActive>
    <isManagedUsingStdDesigner>false</isManagedUsingStdDesigner>
    <name>ST_ProductContainer</name>
    <omniUiCardType>Parent</omniUiCardType>
    <propertySetConfig>{&quot;states&quot;:[{&quot;fields&quot;:[],&quot;conditions&quot;:{&quot;id&quot;:&quot;state-condition-object&quot;,&quot;isParent&quot;:true,&quot;group&quot;:[]},&quot;definedActions&quot;:{&quot;actions&quot;:[]},&quot;name&quot;:&quot;Active&quot;,&quot;isSmartAction&quot;:false,&quot;smartAction&quot;:{},&quot;styleObject&quot;:{&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;x-small&quot;}],&quot;margin&quot;:[{&quot;type&quot;:&quot;bottom&quot;,&quot;size&quot;:&quot;x-small&quot;}],&quot;container&quot;:{&quot;class&quot;:&quot;slds-card&quot;},&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;sizeClass&quot;:&quot;slds-size_12-of-12&quot;,&quot;class&quot;:&quot;slds-card slds-p-around_x-small slds-m-bottom_x-small&quot;},&quot;components&quot;:{&quot;layer-0&quot;:{&quot;children&quot;:[{&quot;name&quot;:&quot;FlexCard&quot;,&quot;element&quot;:&quot;childCardPreview&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;cardName&quot;:&quot;ST_ProductHeader&quot;,&quot;recordId&quot;:&quot;{recordId}&quot;,&quot;cardNode&quot;:&quot;{record}&quot;,&quot;selectedState&quot;:&quot;Active&quot;,&quot;isChildCardTrackingEnabled&quot;:false},&quot;type&quot;:&quot;element&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_12-of-12&quot;},&quot;elementLabel&quot;:&quot;FlexCard-0&quot;},{&quot;name&quot;:&quot;Text&quot;,&quot;element&quot;:&quot;outputField&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;record&quot;:&quot;{record}&quot;,&quot;mergeField&quot;:&quot;%3Cdiv%3E%3Cspan%20style=%22font-size:%2014pt;%22%3E%3Cstrong%3EPreventivi%20e%20lavorazioni%3C/strong%3E%3C/span%3E%3C/div%3E&quot;,&quot;card&quot;:&quot;{card}&quot;,&quot;data-conditions&quot;:{&quot;id&quot;:&quot;state-condition-object&quot;,&quot;isParent&quot;:true,&quot;group&quot;:[{&quot;id&quot;:&quot;state-new-condition-3&quot;,&quot;field&quot;:&quot;domainType&quot;,&quot;operator&quot;:&quot;!=&quot;,&quot;value&quot;:&quot;ESSIG_VITA_PREVIDENZA&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false}]}},&quot;type&quot;:&quot;text&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_12-of-12 &quot;,&quot;padding&quot;:[],&quot;margin&quot;:[{&quot;type&quot;:&quot;bottom&quot;,&quot;size&quot;:&quot;small&quot;,&quot;label&quot;:&quot;bottom:small&quot;}],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;class&quot;:&quot;slds-m-bottom_small &quot;,&quot;style&quot;:&quot;      \n         &quot;},&quot;elementLabel&quot;:&quot;Text-1&quot;,&quot;styleObjects&quot;:[{&quot;key&quot;:0,&quot;conditions&quot;:&quot;default&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_12-of-12 &quot;,&quot;padding&quot;:[],&quot;margin&quot;:[{&quot;type&quot;:&quot;bottom&quot;,&quot;size&quot;:&quot;small&quot;,&quot;label&quot;:&quot;bottom:small&quot;}],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;class&quot;:&quot;slds-m-bottom_small &quot;,&quot;style&quot;:&quot;      \n         &quot;},&quot;label&quot;:&quot;Default&quot;,&quot;name&quot;:&quot;Default&quot;,&quot;conditionString&quot;:&quot;&quot;,&quot;draggable&quot;:false}]},{&quot;name&quot;:&quot;Text&quot;,&quot;element&quot;:&quot;outputField&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;record&quot;:&quot;{record}&quot;,&quot;mergeField&quot;:&quot;%3Cdiv%3E%3Cspan%20style=%22font-size:%2014pt;%22%3E%3Cstrong%3ERiepilogo%20Previdenziale%3C/strong%3E%3C/span%3E%3C/div%3E&quot;,&quot;card&quot;:&quot;{card}&quot;,&quot;data-conditions&quot;:{&quot;id&quot;:&quot;state-condition-object&quot;,&quot;isParent&quot;:true,&quot;group&quot;:[{&quot;id&quot;:&quot;state-new-condition-10&quot;,&quot;field&quot;:&quot;proConfDomainType&quot;,&quot;operator&quot;:&quot;==&quot;,&quot;value&quot;:&quot;ESSIG_VITA_PREVIDENZA&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false}]}},&quot;type&quot;:&quot;text&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_12-of-12 &quot;,&quot;padding&quot;:[],&quot;margin&quot;:[{&quot;type&quot;:&quot;bottom&quot;,&quot;size&quot;:&quot;small&quot;,&quot;label&quot;:&quot;bottom:small&quot;}],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;class&quot;:&quot;slds-m-bottom_small &quot;,&quot;style&quot;:&quot;      \n         &quot;},&quot;elementLabel&quot;:&quot;Text-1-clone-0&quot;,&quot;styleObjects&quot;:[{&quot;key&quot;:0,&quot;conditions&quot;:&quot;default&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_12-of-12 &quot;,&quot;padding&quot;:[],&quot;margin&quot;:[{&quot;type&quot;:&quot;bottom&quot;,&quot;size&quot;:&quot;small&quot;,&quot;label&quot;:&quot;bottom:small&quot;}],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;class&quot;:&quot;slds-m-bottom_small &quot;,&quot;style&quot;:&quot;      \n         &quot;},&quot;label&quot;:&quot;Default&quot;,&quot;name&quot;:&quot;Default&quot;,&quot;conditionString&quot;:&quot;&quot;,&quot;draggable&quot;:false}]},{&quot;name&quot;:&quot;Custom LWC&quot;,&quot;element&quot;:&quot;customLwc&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;targetId&quot;:&quot;{Session.ProductId}&quot;,&quot;productChannel&quot;:&quot;{channel}&quot;,&quot;quoteStored&quot;:&quot;false&quot;,&quot;customlwcname&quot;:&quot;accordionContainerResponsive&quot;},&quot;type&quot;:&quot;element&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_12-of-12&quot;},&quot;elementLabel&quot;:&quot;Custom LWC-2&quot;},{&quot;name&quot;:&quot;Block&quot;,&quot;element&quot;:&quot;block&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;label&quot;:&quot;Altri Preventivi&quot;,&quot;collapsible&quot;:true,&quot;record&quot;:&quot;{record}&quot;,&quot;collapsedByDefault&quot;:true,&quot;card&quot;:&quot;{card}&quot;,&quot;data-conditions&quot;:{&quot;id&quot;:&quot;state-condition-object&quot;,&quot;isParent&quot;:true,&quot;group&quot;:[{&quot;id&quot;:&quot;state-new-condition-0&quot;,&quot;field&quot;:&quot;inactiveQuoteCount&quot;,&quot;operator&quot;:&quot;!=&quot;,&quot;value&quot;:&quot;0&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false}]},&quot;data-preloadConditionalElement&quot;:false},&quot;type&quot;:&quot;block&quot;,&quot;styleObject&quot;:{&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;x-small&quot;,&quot;label&quot;:&quot;around:x-small&quot;}],&quot;class&quot;:&quot;slds-p-around_x-small slds-m-bottom_small &quot;,&quot;sizeClass&quot;:&quot;slds-size_12-of-12 &quot;,&quot;margin&quot;:[{&quot;type&quot;:&quot;bottom&quot;,&quot;size&quot;:&quot;small&quot;,&quot;label&quot;:&quot;bottom:small&quot;}],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;style&quot;:&quot;      \n         &quot;},&quot;children&quot;:[{&quot;key&quot;:&quot;element_element_block_3_0_customLwc_0_0&quot;,&quot;name&quot;:&quot;Custom LWC&quot;,&quot;element&quot;:&quot;customLwc&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;targetId&quot;:&quot;{Session.ProductId}&quot;,&quot;quoteStored&quot;:&quot;true&quot;,&quot;productChannel&quot;:&quot;{channel}&quot;,&quot;otherQuotes&quot;:&quot;true&quot;,&quot;hasMultipleQuotes&quot;:&quot;false&quot;,&quot;customlwcname&quot;:&quot;accordionContainerResponsive&quot;},&quot;type&quot;:&quot;element&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_12-of-12&quot;},&quot;parentElementKey&quot;:&quot;element_block_3_0&quot;,&quot;elementLabel&quot;:&quot;Block-3-Custom LWC-0&quot;},{&quot;key&quot;:&quot;element_element_block_3_0_block_1_0&quot;,&quot;name&quot;:&quot;Block&quot;,&quot;element&quot;:&quot;block&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;label&quot;:&quot;Block&quot;,&quot;collapsible&quot;:false,&quot;record&quot;:&quot;{record}&quot;,&quot;collapsedByDefault&quot;:false,&quot;card&quot;:&quot;{card}&quot;},&quot;type&quot;:&quot;block&quot;,&quot;styleObject&quot;:{&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;x-small&quot;,&quot;label&quot;:&quot;around:x-small&quot;}],&quot;class&quot;:&quot;slds-p-around_x-small &quot;,&quot;sizeClass&quot;:&quot;slds-size_12-of-12 &quot;,&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;height:0;&quot;,&quot;style&quot;:&quot;      \n         height:0;&quot;},&quot;children&quot;:[],&quot;parentElementKey&quot;:&quot;element_block_3_0&quot;,&quot;elementLabel&quot;:&quot;Block-3-Block-1&quot;,&quot;styleObjects&quot;:[{&quot;key&quot;:0,&quot;conditions&quot;:&quot;default&quot;,&quot;styleObject&quot;:{&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;x-small&quot;,&quot;label&quot;:&quot;around:x-small&quot;}],&quot;class&quot;:&quot;slds-p-around_x-small &quot;,&quot;sizeClass&quot;:&quot;slds-size_12-of-12 &quot;,&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;height:0;&quot;,&quot;style&quot;:&quot;      \n         height:0;&quot;},&quot;label&quot;:&quot;Default&quot;,&quot;name&quot;:&quot;Default&quot;,&quot;conditionString&quot;:&quot;&quot;,&quot;draggable&quot;:false}]}],&quot;elementLabel&quot;:&quot;Block-3&quot;,&quot;styleObjects&quot;:[{&quot;key&quot;:0,&quot;conditions&quot;:&quot;default&quot;,&quot;styleObject&quot;:{&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;x-small&quot;,&quot;label&quot;:&quot;around:x-small&quot;}],&quot;class&quot;:&quot;slds-p-around_x-small slds-m-bottom_small &quot;,&quot;sizeClass&quot;:&quot;slds-size_12-of-12 &quot;,&quot;margin&quot;:[{&quot;type&quot;:&quot;bottom&quot;,&quot;size&quot;:&quot;small&quot;,&quot;label&quot;:&quot;bottom:small&quot;}],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;style&quot;:&quot;      \n         &quot;},&quot;label&quot;:&quot;Default&quot;,&quot;name&quot;:&quot;Default&quot;,&quot;conditionString&quot;:&quot;&quot;,&quot;draggable&quot;:false}]}]}},&quot;childCards&quot;:[&quot;ST_ProductHeader&quot;],&quot;actions&quot;:[],&quot;omniscripts&quot;:[],&quot;documents&quot;:[]}],&quot;dataSource&quot;:{&quot;type&quot;:&quot;IntegrationProcedures&quot;,&quot;value&quot;:{&quot;dsDelay&quot;:&quot;&quot;,&quot;ipMethod&quot;:&quot;LeadManagement_RetrieveProductConfiguration&quot;,&quot;vlocityAsync&quot;:false,&quot;inputMap&quot;:{&quot;recordId&quot;:&quot;{Session.ProductId}&quot;},&quot;jsonMap&quot;:&quot;{\&quot;Session.ProductId\&quot;:\&quot;{Session.ProductId}\&quot;}&quot;,&quot;resultVar&quot;:&quot;&quot;},&quot;orderBy&quot;:{&quot;name&quot;:&quot;&quot;,&quot;isReverse&quot;:&quot;&quot;},&quot;contextVariables&quot;:[{&quot;name&quot;:&quot;Session.ProductId&quot;,&quot;val&quot;:&quot;0069O00000S2clqQAB&quot;,&quot;id&quot;:4}]},&quot;title&quot;:&quot;ST_ProductContainer&quot;,&quot;enableLwc&quot;:true,&quot;isFlex&quot;:true,&quot;theme&quot;:&quot;slds&quot;,&quot;selectableMode&quot;:&quot;Multi&quot;,&quot;lwc&quot;:{&quot;DeveloperName&quot;:&quot;cfST_ProductContainer_6_Unipolsai&quot;,&quot;Id&quot;:&quot;0Rb9O000003HEBJSA4&quot;,&quot;MasterLabel&quot;:&quot;cfST_ProductContainer_6_Unipolsai&quot;,&quot;NamespacePrefix&quot;:&quot;c&quot;,&quot;ManageableState&quot;:&quot;unmanaged&quot;},&quot;xmlObject&quot;:{&quot;targetConfigs&quot;:&quot;PHRhcmdldENvbmZpZyAgdGFyZ2V0cz0ibGlnaHRuaW5nX19SZWNvcmRQYWdlIj48cHJvcGVydHkgIG5hbWU9ImRlYnVnIiB0eXBlPSJCb29sZWFuIj48L3Byb3BlcnR5Pjxwcm9wZXJ0eSAgbmFtZT0icmVjb3JkSWQiIHR5cGU9IlN0cmluZyI+PC9wcm9wZXJ0eT48cHJvcGVydHkgIG5hbWU9ImNmUHJvZHVjdElkIiB0eXBlPSJTdHJpbmciIGxhYmVsPSJQcm9kdWN0SWQiIHJlcXVpcmVkPSJ0cnVlIj48L3Byb3BlcnR5PjwvdGFyZ2V0Q29uZmlnPjx0YXJnZXRDb25maWcgIHRhcmdldHM9ImxpZ2h0bmluZ19fQXBwUGFnZSI+PHByb3BlcnR5ICBuYW1lPSJkZWJ1ZyIgdHlwZT0iQm9vbGVhbiI+PC9wcm9wZXJ0eT48cHJvcGVydHkgIG5hbWU9InJlY29yZElkIiB0eXBlPSJTdHJpbmciPjwvcHJvcGVydHk+PHByb3BlcnR5ICBuYW1lPSJjZlByb2R1Y3RJZCIgdHlwZT0iU3RyaW5nIiBsYWJlbD0iUHJvZHVjdElkIiByZXF1aXJlZD0idHJ1ZSI+PC9wcm9wZXJ0eT48L3RhcmdldENvbmZpZz48dGFyZ2V0Q29uZmlnICB0YXJnZXRzPSJsaWdodG5pbmdfX0hvbWVQYWdlIj48cHJvcGVydHkgIG5hbWU9ImNmUHJvZHVjdElkIiB0eXBlPSJTdHJpbmciIGxhYmVsPSJQcm9kdWN0SWQiIHJlcXVpcmVkPSJ0cnVlIj48L3Byb3BlcnR5PjwvdGFyZ2V0Q29uZmlnPg==&quot;,&quot;targets&quot;:{&quot;target&quot;:[&quot;lightning__RecordPage&quot;,&quot;lightning__AppPage&quot;,&quot;lightning__HomePage&quot;]},&quot;apiVersion&quot;:56,&quot;masterLabel&quot;:&quot;ST_ProductContainer&quot;,&quot;description&quot;:&quot;&quot;,&quot;runtimeNamespace&quot;:&quot;&quot;,&quot;isExposed&quot;:true},&quot;xmlJson&quot;:[{&quot;@attributes&quot;:{&quot;targets&quot;:&quot;lightning__RecordPage&quot;},&quot;property&quot;:[{&quot;@attributes&quot;:{&quot;name&quot;:&quot;debug&quot;,&quot;type&quot;:&quot;Boolean&quot;}},{&quot;@attributes&quot;:{&quot;name&quot;:&quot;recordId&quot;,&quot;type&quot;:&quot;String&quot;}},{&quot;@attributes&quot;:{&quot;name&quot;:&quot;cfProductId&quot;,&quot;type&quot;:&quot;String&quot;,&quot;label&quot;:&quot;ProductId&quot;,&quot;required&quot;:true}}]},{&quot;@attributes&quot;:{&quot;targets&quot;:&quot;lightning__AppPage&quot;},&quot;property&quot;:[{&quot;@attributes&quot;:{&quot;name&quot;:&quot;debug&quot;,&quot;type&quot;:&quot;Boolean&quot;}},{&quot;@attributes&quot;:{&quot;name&quot;:&quot;recordId&quot;,&quot;type&quot;:&quot;String&quot;}},{&quot;@attributes&quot;:{&quot;name&quot;:&quot;cfProductId&quot;,&quot;type&quot;:&quot;String&quot;,&quot;label&quot;:&quot;ProductId&quot;,&quot;required&quot;:true}}]},{&quot;@attributes&quot;:{&quot;targets&quot;:&quot;lightning__HomePage&quot;},&quot;property&quot;:[{&quot;@attributes&quot;:{&quot;name&quot;:&quot;cfProductId&quot;,&quot;type&quot;:&quot;String&quot;,&quot;label&quot;:&quot;ProductId&quot;,&quot;required&quot;:true}}]}],&quot;isRepeatable&quot;:true,&quot;osSupport&quot;:true,&quot;sessionVars&quot;:[{&quot;name&quot;:&quot;ProductId&quot;,&quot;isApi&quot;:true,&quot;val&quot;:&quot;&quot;}],&quot;events&quot;:[{&quot;eventname&quot;:&quot;refreshView&quot;,&quot;channelname&quot;:&quot;ST_ProductContainer&quot;,&quot;element&quot;:&quot;action&quot;,&quot;eventtype&quot;:&quot;event&quot;,&quot;recordIndex&quot;:&quot;0&quot;,&quot;actionList&quot;:[{&quot;key&quot;:&quot;1749020150539-ry9eud0bd&quot;,&quot;label&quot;:&quot;Action&quot;,&quot;draggable&quot;:false,&quot;isOpen&quot;:true,&quot;card&quot;:&quot;{card}&quot;,&quot;stateAction&quot;:{&quot;id&quot;:&quot;flex-action-1749020150625&quot;,&quot;type&quot;:&quot;cardAction&quot;,&quot;displayName&quot;:&quot;Action&quot;,&quot;vlocityIcon&quot;:&quot;standard-default&quot;,&quot;targetType&quot;:&quot;Web Page&quot;,&quot;openUrlIn&quot;:&quot;Current Window&quot;,&quot;Web Page&quot;:{&quot;targetName&quot;:&quot;/apex&quot;},&quot;eventName&quot;:&quot;reload&quot;},&quot;actionIndex&quot;:0}],&quot;showSpinner&quot;:&quot;false&quot;,&quot;key&quot;:&quot;event-0&quot;,&quot;displayLabel&quot;:&quot;refreshView&quot;,&quot;eventLabel&quot;:&quot;custom event&quot;}]}</propertySetConfig>
    <sampleDataSourceResponse>{&quot;inactiveQuoteCount&quot;:0,&quot;noteCount&quot;:0,&quot;notes&quot;:[],&quot;wasReassigned&quot;:&quot;No&quot;,&quot;domainType&quot;:&quot;PU&quot;,&quot;engagementPoint&quot;:&quot;Digitale Unica&quot;,&quot;channel&quot;:&quot;Preventivatore digitale Unica&quot;,&quot;proConfDomainType&quot;:&quot;PU&quot;,&quot;source&quot;:&quot;Preventivatore digitale Unica&quot;,&quot;parentId&quot;:&quot;0069O00000S2clpQAB&quot;,&quot;canaleDiVendita&quot;:&quot;Agenzia&quot;,&quot;productCode&quot;:&quot;Unica&quot;,&quot;quotesAllowed&quot;:&quot;true&quot;,&quot;saleChannel&quot;:&quot;-&quot;,&quot;callMeBackAllowed&quot;:&quot;true&quot;,&quot;id&quot;:&quot;0069O00000S2clqQAB&quot;,&quot;status&quot;:&quot;Assegnato&quot;}</sampleDataSourceResponse>
    <versionNumber>6</versionNumber>
</OmniUiCard>
