<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata">
    <apiVersion>64.0</apiVersion>
    <areMetricsLoggedToDataCloud>false</areMetricsLoggedToDataCloud>
    <assignments>
        <name>Collect_ur_RT_id</name>
        <label>Collect ur RT id</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <assignmentItems>
            <assignToReference>urRTidList</assignToReference>
            <operator>Add</operator>
            <value>
                <elementReference>Loop_RTCaseInfo.Id</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Loop_RTCaseInfo</targetReference>
        </connector>
    </assignments>
    <decisions>
        <name>Check_Case_current_status</name>
        <label>Check Case current status &amp;&amp; currentdatetime</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>In_attesa_risposta_cliente</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>GetCaseInfo.Status</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>In attesa risposta cliente</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Flow.CurrentDateTime</leftValueReference>
                <operator>LessThanOrEqualTo</operator>
                <rightValue>
                    <elementReference>EmailMsgCreateddatePlus7Days</elementReference>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>UpdateCaseStatus_Ricevuta_risp_cliente</targetReference>
            </connector>
            <label>In attesa risposta cliente</label>
        </rules>
    </decisions>
    <decisions>
        <name>Check_Iif_is_URCS_Case</name>
        <label>Check Iif is URCS Case</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>true</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>GetCaseInfo</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Decision_4</targetReference>
            </connector>
            <label>true</label>
        </rules>
    </decisions>
    <decisions>
        <name>Copy_2_of_Check_Case_current_status</name>
        <label>Check Case current status</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>Copy_2_of_In_gestione</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>GetCaseInfo.Status</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>In gestione</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.InAttesaRispCliente__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <elementReference>true</elementReference>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>UpdateCaseStatusInattesarispcliente</targetReference>
            </connector>
            <label>In gestione</label>
        </rules>
    </decisions>
    <decisions>
        <name>Decision_4</name>
        <label>Email msg is?</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>Outcome_1_of_Decision_4</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>$Record.Incoming</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Status</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>3</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Copy_2_of_Check_Case_current_status</targetReference>
            </connector>
            <label>Sent</label>
        </rules>
        <rules>
            <name>Received</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>$Record.Incoming</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Status</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>0</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Check_Case_current_status</targetReference>
            </connector>
            <label>Received</label>
        </rules>
    </decisions>
    <environments>Default</environments>
    <formulas>
        <name>EmailMsgCreateddatePlus7Days</name>
        <dataType>DateTime</dataType>
        <expression>{!$Record.CreatedDate} +7</expression>
    </formulas>
    <interviewLabel>urcs_RTEmailMessageAfterIU {!$Flow.CurrentDateTime}</interviewLabel>
    <label>urcs_RTEmailMessageAfterIU</label>
    <loops>
        <name>Loop_RTCaseInfo</name>
        <label>Loop RTCaseInfo</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <collectionReference>getRTCaseInfo</collectionReference>
        <iterationOrder>Asc</iterationOrder>
        <nextValueConnector>
            <targetReference>Collect_ur_RT_id</targetReference>
        </nextValueConnector>
        <noMoreValuesConnector>
            <targetReference>GetCaseInfo</targetReference>
        </noMoreValuesConnector>
    </loops>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>AUTO_LAYOUT_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>AutoLaunchedFlow</processType>
    <recordLookups>
        <name>GetCaseInfo</name>
        <label>GetCaseInfo</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Check_Iif_is_URCS_Case</targetReference>
        </connector>
        <filterLogic>1 AND 2 AND (3 OR 4)</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>$Record.ParentId</elementReference>
            </value>
        </filters>
        <filters>
            <field>RecordTypeId</field>
            <operator>In</operator>
            <value>
                <elementReference>urRTidList</elementReference>
            </value>
        </filters>
        <filters>
            <field>Status</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>In gestione</stringValue>
            </value>
        </filters>
        <filters>
            <field>Status</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>In attesa risposta cliente</stringValue>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>Case</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>getRTCaseInfo</name>
        <label>getRTCaseInfo</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Loop_RTCaseInfo</targetReference>
        </connector>
        <filterLogic>or</filterLogic>
        <filters>
            <field>DeveloperName</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>ur_CaseCRM</stringValue>
            </value>
        </filters>
        <filters>
            <field>DeveloperName</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>ur_CaseAR</stringValue>
            </value>
        </filters>
        <filters>
            <field>DeveloperName</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>ur_CaseSitoWeb</stringValue>
            </value>
        </filters>
        <filters>
            <field>DeveloperName</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>ur_CasePQ</stringValue>
            </value>
        </filters>
        <getFirstRecordOnly>false</getFirstRecordOnly>
        <object>RecordType</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordUpdates>
        <name>UpdateCaseStatus_Ricevuta_risp_cliente</name>
        <label>UpdateCaseStatus: Ricevuta risp cliente</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>GetCaseInfo.Id</elementReference>
            </value>
        </filters>
        <inputAssignments>
            <field>Status</field>
            <value>
                <stringValue>Ricevuta risposta cliente</stringValue>
            </value>
        </inputAssignments>
        <object>Case</object>
    </recordUpdates>
    <recordUpdates>
        <name>UpdateCaseStatusInattesarispcliente</name>
        <label>UpdateCaseStatus: In attesa risp cliente</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>GetCaseInfo.Id</elementReference>
            </value>
        </filters>
        <inputAssignments>
            <field>Status</field>
            <value>
                <stringValue>In attesa risposta cliente</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>dtInAttesaRispCliente__c</field>
            <value>
                <elementReference>$Flow.CurrentDate</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>EmailSollecito__c</field>
            <value>
                <elementReference>$Record.ToAddress</elementReference>
            </value>
        </inputAssignments>
        <object>Case</object>
    </recordUpdates>
    <start>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <connector>
            <targetReference>getRTCaseInfo</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>ParentId</field>
            <operator>IsNull</operator>
            <value>
                <booleanValue>false</booleanValue>
            </value>
        </filters>
        <object>EmailMessage</object>
        <recordTriggerType>CreateAndUpdate</recordTriggerType>
        <triggerType>RecordAfterSave</triggerType>
    </start>
    <status>Active</status>
    <variables>
        <name>urRTidList</name>
        <dataType>String</dataType>
        <isCollection>true</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
    </variables>
</Flow>
