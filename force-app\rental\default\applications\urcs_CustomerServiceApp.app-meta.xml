<?xml version="1.0" encoding="UTF-8"?>
<CustomApplication xmlns="http://soap.sforce.com/2006/04/metadata">
    <actionOverrides>
        <actionName>Tab</actionName>
        <content>urcs_CustomerServiceHomePage</content>
        <formFactor>Large</formFactor>
        <skipRecordTypeSelect>false</skipRecordTypeSelect>
        <type>Flexipage</type>
        <pageOrSobjectType>standard-home</pageOrSobjectType>
    </actionOverrides>
    <brand>
        <headerColor>#1C3A44</headerColor>
        <logo>Unipol_Gruppo_Logosvg1</logo>
        <logoVersion>1</logoVersion>
        <shouldOverrideOrgTheme>false</shouldOverrideOrgTheme>
    </brand>
    <formFactors>Small</formFactors>
    <formFactors>Large</formFactors>
    <isNavAutoTempTabsDisabled>false</isNavAutoTempTabsDisabled>
    <isNavPersonalizationDisabled>false</isNavPersonalizationDisabled>
    <isNavTabPersistenceDisabled>false</isNavTabPersistenceDisabled>
    <isOmniPinnedViewEnabled>false</isOmniPinnedViewEnabled>
    <label>Unipol Rental - Customer Service</label>
    <navType>Console</navType>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>urcs_Cliente</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.FinServ__IndustriesBusiness</recordType>
        <type>Flexipage</type>
        <profile>Admin</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>urcs_Cliente</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.FinServ__IndustriesBusiness</recordType>
        <type>Flexipage</type>
        <profile>Admin</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>urcs_Cliente</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>PersonAccount.PersonAccount</recordType>
        <type>Flexipage</type>
        <profile>Admin</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>urcs_Cliente</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>PersonAccount.PersonAccount</recordType>
        <type>Flexipage</type>
        <profile>Admin</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>urcs_Utilizzatore</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>PersonAccount.ur_Utilizzatore</recordType>
        <type>Flexipage</type>
        <profile>Admin</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>urcs_Utilizzatore</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>PersonAccount.ur_Utilizzatore</recordType>
        <type>Flexipage</type>
        <profile>Admin</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>urcs_FleetManager</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>Contact</pageOrSobjectType>
        <recordType>Contact.ur_FleetManager</recordType>
        <type>Flexipage</type>
        <profile>Admin</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>urcs_FleetManager</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Contact</pageOrSobjectType>
        <recordType>Contact.ur_FleetManager</recordType>
        <type>Flexipage</type>
        <profile>Admin</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>urcs_Cliente</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.FinServ__IndustriesBusiness</recordType>
        <type>Flexipage</type>
        <profile>Unipol Rental CS Standard User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>urcs_Cliente</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.FinServ__IndustriesBusiness</recordType>
        <type>Flexipage</type>
        <profile>Unipol Rental CS Standard User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>urcs_Cliente</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>PersonAccount.PersonAccount</recordType>
        <type>Flexipage</type>
        <profile>Unipol Rental CS Standard User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>urcs_Cliente</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>PersonAccount.PersonAccount</recordType>
        <type>Flexipage</type>
        <profile>Unipol Rental CS Standard User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>urcs_Utilizzatore</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>PersonAccount.ur_Utilizzatore</recordType>
        <type>Flexipage</type>
        <profile>Unipol Rental CS Standard User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>urcs_Utilizzatore</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>PersonAccount.ur_Utilizzatore</recordType>
        <type>Flexipage</type>
        <profile>Unipol Rental CS Standard User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>urcs_FleetManager</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>Contact</pageOrSobjectType>
        <recordType>Contact.ur_FleetManager</recordType>
        <type>Flexipage</type>
        <profile>Unipol Rental CS Standard User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>urcs_FleetManager</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Contact</pageOrSobjectType>
        <recordType>Contact.ur_FleetManager</recordType>
        <type>Flexipage</type>
        <profile>Unipol Rental CS Standard User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>urcs_ServiceContract</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>ServiceContract</pageOrSobjectType>
        <recordType>ServiceContract.ur_Quadro</recordType>
        <type>Flexipage</type>
        <profile>Admin</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>urcs_ServiceContract</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>ServiceContract</pageOrSobjectType>
        <recordType>ServiceContract.ur_Quadro</recordType>
        <type>Flexipage</type>
        <profile>Admin</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>urcs_ServiceContract</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>ServiceContract</pageOrSobjectType>
        <recordType>ServiceContract.ur_Quadro</recordType>
        <type>Flexipage</type>
        <profile>Unipol Rental CS Standard User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>urcs_ServiceContract</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>ServiceContract</pageOrSobjectType>
        <recordType>ServiceContract.ur_Quadro</recordType>
        <type>Flexipage</type>
        <profile>Unipol Rental CS Standard User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>urcs_ServiceContract</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>ServiceContract</pageOrSobjectType>
        <recordType>ServiceContract.ur_Singolo</recordType>
        <type>Flexipage</type>
        <profile>Admin</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>urcs_ServiceContract</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>ServiceContract</pageOrSobjectType>
        <recordType>ServiceContract.ur_Singolo</recordType>
        <type>Flexipage</type>
        <profile>Admin</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>urcs_ServiceContract</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>ServiceContract</pageOrSobjectType>
        <recordType>ServiceContract.ur_Singolo</recordType>
        <type>Flexipage</type>
        <profile>Unipol Rental CS Standard User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>urcs_ServiceContract</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>ServiceContract</pageOrSobjectType>
        <recordType>ServiceContract.ur_Singolo</recordType>
        <type>Flexipage</type>
        <profile>Unipol Rental CS Standard User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>urcs_ContractDriver</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>ContractDriver__c</pageOrSobjectType>
        <recordType>ContractDriver__c.ur_UtilizzatoreContratto</recordType>
        <type>Flexipage</type>
        <profile>Admin</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>urcs_ContractDriver</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>ContractDriver__c</pageOrSobjectType>
        <recordType>ContractDriver__c.ur_UtilizzatoreContratto</recordType>
        <type>Flexipage</type>
        <profile>Admin</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>urcs_ContractDriver</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>ContractDriver__c</pageOrSobjectType>
        <recordType>ContractDriver__c.ur_UtilizzatoreContratto</recordType>
        <type>Flexipage</type>
        <profile>Unipol Rental CS Standard User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>urcs_ContractDriver</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>ContractDriver__c</pageOrSobjectType>
        <recordType>ContractDriver__c.ur_UtilizzatoreContratto</recordType>
        <type>Flexipage</type>
        <profile>Unipol Rental CS Standard User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>urcs_ContractDriver</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>ContractDriver__c</pageOrSobjectType>
        <type>Flexipage</type>
        <profile>Admin</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>urcs_ContractDriver</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>ContractDriver__c</pageOrSobjectType>
        <type>Flexipage</type>
        <profile>Admin</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>urcs_ContractDriver</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>ContractDriver__c</pageOrSobjectType>
        <type>Flexipage</type>
        <profile>Unipol Rental CS Standard User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>urcs_ContractDriver</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>ContractDriver__c</pageOrSobjectType>
        <type>Flexipage</type>
        <profile>Unipol Rental CS Standard User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>urcs_Veicolo</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>Asset</pageOrSobjectType>
        <recordType>Asset.ur_Veicolo</recordType>
        <type>Flexipage</type>
        <profile>Admin</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>urcs_Veicolo</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Asset</pageOrSobjectType>
        <recordType>Asset.ur_Veicolo</recordType>
        <type>Flexipage</type>
        <profile>Admin</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>urcs_ContractAsset</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>ContractAsset__c</pageOrSobjectType>
        <recordType>ContractAsset__c.ur_VeicoloContratto</recordType>
        <type>Flexipage</type>
        <profile>Unipol Rental CS Standard User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>urcs_ContractAsset</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>ContractAsset__c</pageOrSobjectType>
        <recordType>ContractAsset__c.ur_VeicoloContratto</recordType>
        <type>Flexipage</type>
        <profile>Unipol Rental CS Standard User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>urcs_ContractAsset</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>ContractAsset__c</pageOrSobjectType>
        <recordType>ContractAsset__c.ur_VeicoloContratto</recordType>
        <type>Flexipage</type>
        <profile>Admin</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>urcs_ContractAsset</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>ContractAsset__c</pageOrSobjectType>
        <recordType>ContractAsset__c.ur_VeicoloContratto</recordType>
        <type>Flexipage</type>
        <profile>Admin</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>urcs_Indirizzo</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>Address__c</pageOrSobjectType>
        <recordType>Address__c.ur_Indirizzo</recordType>
        <type>Flexipage</type>
        <profile>Unipol Rental CS Standard User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>urcs_Indirizzo</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Address__c</pageOrSobjectType>
        <recordType>Address__c.ur_Indirizzo</recordType>
        <type>Flexipage</type>
        <profile>Unipol Rental CS Standard User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>urcs_Indirizzo</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>Address__c</pageOrSobjectType>
        <recordType>Address__c.ur_Indirizzo</recordType>
        <type>Flexipage</type>
        <profile>Admin</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>urcs_Indirizzo</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Address__c</pageOrSobjectType>
        <recordType>Address__c.ur_Indirizzo</recordType>
        <type>Flexipage</type>
        <profile>Admin</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>urcs_Veicolo</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>Asset</pageOrSobjectType>
        <recordType>Asset.ur_Veicolo</recordType>
        <type>Flexipage</type>
        <profile>Unipol Rental CS Standard User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>urcs_Veicolo</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Asset</pageOrSobjectType>
        <recordType>Asset.ur_Veicolo</recordType>
        <type>Flexipage</type>
        <profile>Unipol Rental CS Standard User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>urcs_Case</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>Case</pageOrSobjectType>
        <recordType>Case.ur_CaseAR</recordType>
        <type>Flexipage</type>
        <profile>Admin</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>urcs_Case</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Case</pageOrSobjectType>
        <recordType>Case.ur_CaseAR</recordType>
        <type>Flexipage</type>
        <profile>Admin</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>urcs_Case</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>Case</pageOrSobjectType>
        <recordType>Case.ur_CaseAR</recordType>
        <type>Flexipage</type>
        <profile>Unipol Rental CS Standard User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>urcs_Case</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Case</pageOrSobjectType>
        <recordType>Case.ur_CaseAR</recordType>
        <type>Flexipage</type>
        <profile>Unipol Rental CS Standard User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>urcs_Case</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>Case</pageOrSobjectType>
        <recordType>Case.ur_CaseCRM</recordType>
        <type>Flexipage</type>
        <profile>Admin</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>urcs_Case</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Case</pageOrSobjectType>
        <recordType>Case.ur_CaseCRM</recordType>
        <type>Flexipage</type>
        <profile>Admin</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>urcs_Case</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>Case</pageOrSobjectType>
        <recordType>Case.ur_CaseCRM</recordType>
        <type>Flexipage</type>
        <profile>Unipol Rental CS Standard User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>urcs_Case</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Case</pageOrSobjectType>
        <recordType>Case.ur_CaseCRM</recordType>
        <type>Flexipage</type>
        <profile>Unipol Rental CS Standard User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>urcs_Case</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>Case</pageOrSobjectType>
        <recordType>Case.ur_CasePQ</recordType>
        <type>Flexipage</type>
        <profile>Admin</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>urcs_Case</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Case</pageOrSobjectType>
        <recordType>Case.ur_CasePQ</recordType>
        <type>Flexipage</type>
        <profile>Admin</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>urcs_Case</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>Case</pageOrSobjectType>
        <recordType>Case.ur_CasePQ</recordType>
        <type>Flexipage</type>
        <profile>Unipol Rental CS Standard User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>urcs_Case</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Case</pageOrSobjectType>
        <recordType>Case.ur_CasePQ</recordType>
        <type>Flexipage</type>
        <profile>Unipol Rental CS Standard User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>urcs_Case</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>Case</pageOrSobjectType>
        <recordType>Case.ur_CaseSitoWeb</recordType>
        <type>Flexipage</type>
        <profile>Admin</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>urcs_Case</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Case</pageOrSobjectType>
        <recordType>Case.ur_CaseSitoWeb</recordType>
        <type>Flexipage</type>
        <profile>Admin</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>urcs_Case</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>Case</pageOrSobjectType>
        <recordType>Case.ur_CaseSitoWeb</recordType>
        <type>Flexipage</type>
        <profile>Unipol Rental CS Standard User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>urcs_Case</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Case</pageOrSobjectType>
        <recordType>Case.ur_CaseSitoWeb</recordType>
        <type>Flexipage</type>
        <profile>Unipol Rental CS Standard User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>urcs_Societa</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Society</recordType>
        <type>Flexipage</type>
        <profile>Admin</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>urcs_Societa</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Society</recordType>
        <type>Flexipage</type>
        <profile>Admin</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>urcs_Societa</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Society</recordType>
        <type>Flexipage</type>
        <profile>Unipol Rental CS Standard User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>urcs_Societa</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Society</recordType>
        <type>Flexipage</type>
        <profile>Unipol Rental CS Standard User</profile>
    </profileActionOverrides>
    <tabs>standard-home</tabs>
    <tabs>standard-Account</tabs>
    <tabs>standard-Asset</tabs>
    <tabs>standard-ServiceContract</tabs>
    <tabs>standard-Case</tabs>
    <tabs>standard-report</tabs>
    <uiType>Lightning</uiType>
    <utilityBar>urcs_CustomerServiceApp_UtilityBar</utilityBar>
    <workspaceConfig>
        <mappings>
            <tab>standard-Account</tab>
        </mappings>
        <mappings>
            <tab>standard-Asset</tab>
        </mappings>
        <mappings>
            <tab>standard-Case</tab>
        </mappings>
        <mappings>
            <tab>standard-ServiceContract</tab>
        </mappings>
        <mappings>
            <tab>standard-home</tab>
        </mappings>
        <mappings>
            <tab>standard-report</tab>
        </mappings>
    </workspaceConfig>
</CustomApplication>
