<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata">
    <actionCalls>
        <name>Copy_1_of_Send_Case_Email</name>
        <label>Copy 1 of Send Case Email</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <actionName>emailSimple</actionName>
        <actionType>emailSimple</actionType>
        <flowTransactionModel>CurrentTransaction</flowTransactionModel>
        <inputParameters>
            <name>senderType</name>
            <value>
                <stringValue>OrgWideEmailAddress</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>senderAddress</name>
            <value>
                <stringValue><EMAIL></stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>useEmailTemplate</name>
            <value>
                <stringValue>True</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>emailTemplateId</name>
            <value>
                <stringValue>00X9O000006sVTBUA2</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>recipientId</name>
            <value>
                <elementReference>$Record.ContactId</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>relatedRecordId</name>
            <value>
                <elementReference>$Record.Id</elementReference>
            </value>
        </inputParameters>
        <nameSegment>emailSimple</nameSegment>
        <offset>0</offset>
        <versionString>1.0.1</versionString>
    </actionCalls>
    <actionCalls>
        <name>Send_Case_Email</name>
        <label>Send Case Email</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <actionName>emailSimple</actionName>
        <actionType>emailSimple</actionType>
        <flowTransactionModel>CurrentTransaction</flowTransactionModel>
        <inputParameters>
            <name>senderType</name>
            <value>
                <stringValue>OrgWideEmailAddress</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>senderAddress</name>
            <value>
                <stringValue><EMAIL></stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>useEmailTemplate</name>
            <value>
                <stringValue>True</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>emailTemplateId</name>
            <value>
                <stringValue>00X9O000006sV8DUAU</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>recipientId</name>
            <value>
                <elementReference>$Record.ContactId</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>relatedRecordId</name>
            <value>
                <elementReference>$Record.Id</elementReference>
            </value>
        </inputParameters>
        <nameSegment>emailSimple</nameSegment>
        <offset>0</offset>
        <versionString>1.0.1</versionString>
    </actionCalls>
    <apiVersion>64.0</apiVersion>
    <areMetricsLoggedToDataCloud>false</areMetricsLoggedToDataCloud>
    <decisions>
        <name>Check_Email_Conditions</name>
        <label>Check Email Conditions</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>Send_Email</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>$Record.InvioEmailAutomatico__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Risolto__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.ContactId</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Check_Origin</targetReference>
            </connector>
            <label>Send Email</label>
        </rules>
    </decisions>
    <decisions>
        <name>Check_if_is_insert_or_update</name>
        <label>Check if is insert or update</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <defaultConnector>
            <targetReference>Is_chiuso_risolto</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Is after Update</defaultConnectorLabel>
        <rules>
            <name>Is_after_insert</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>is_new</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Check_Email_Conditions</targetReference>
            </connector>
            <label>Is after insert</label>
        </rules>
    </decisions>
    <decisions>
        <name>Check_Origin</name>
        <label>Check Origin</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>Valid_Origin</name>
            <conditionLogic>or</conditionLogic>
            <conditions>
                <leftValueReference>$Record.Origin</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Phone</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Origin</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Web</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Origin</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>PEC</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Origin</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Social</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Origin</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Area Riservata</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Origin</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Portale Quotazioni</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Origin</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Email</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Send_Case_Email</targetReference>
            </connector>
            <label>Valid Origin</label>
        </rules>
    </decisions>
    <decisions>
        <name>Copy_1_of_Check_Email_Conditions</name>
        <label>Copy 1 of Check Email Conditions</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>Copy_1_of_Send_Email</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>$Record.InvioEmailAutomatico__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.ContactId</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Copy_1_of_Check_Origin</targetReference>
            </connector>
            <label>Copy 1 of Send Email</label>
        </rules>
    </decisions>
    <decisions>
        <name>Copy_1_of_Check_Origin</name>
        <label>Copy 1 of Check Origin</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>Copy_1_of_Valid_Origin</name>
            <conditionLogic>or</conditionLogic>
            <conditions>
                <leftValueReference>$Record.Origin</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Phone</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Origin</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Web</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Origin</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>PEC</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Origin</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Social</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Origin</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Area Riservata</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Origin</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Portale Quotazioni</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Origin</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Email</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Copy_1_of_Send_Case_Email</targetReference>
            </connector>
            <label>Copy 1 of Valid Origin</label>
        </rules>
    </decisions>
    <decisions>
        <name>Is_chiuso_risolto</name>
        <label>Is chiuso risolto</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <defaultConnectorLabel>NO</defaultConnectorLabel>
        <rules>
            <name>YES</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>$Record.IsClosed</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.IsClosed</leftValueReference>
                <operator>IsChanged</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Status</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Chiuso - Risolto</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Copy_1_of_Check_Email_Conditions</targetReference>
            </connector>
            <label>YES</label>
        </rules>
    </decisions>
    <environments>Default</environments>
    <formulas>
        <name>is_new</name>
        <dataType>Boolean</dataType>
        <expression>ISNEW()</expression>
    </formulas>
    <interviewLabel>urcs_RTCaseAfterIU {!$Flow.CurrentDateTime}</interviewLabel>
    <label>urcs_RTCaseAfterIU</label>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>AUTO_LAYOUT_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>AutoLaunchedFlow</processType>
    <start>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <connector>
            <targetReference>Check_if_is_insert_or_update</targetReference>
        </connector>
        <filterFormula>IF(
    AND(
        OR(
        {!$Record.RecordType.DeveloperName} == &apos;ur_CaseCRM&apos;,
        {!$Record.RecordType.DeveloperName} ==  &apos;ur_CaseAR&apos;,
        {!$Record.RecordType.DeveloperName} == &apos;ur_CasePQ&apos;,
        {!$Record.RecordType.DeveloperName} == &apos;ur_CaseSitoWeb&apos;
        )
    ,
        {!$Setup.urcs_GeneralSettings__c.SkipTrigger__c} == false
    ),
true,false
)</filterFormula>
        <object>Case</object>
        <recordTriggerType>CreateAndUpdate</recordTriggerType>
        <triggerType>RecordAfterSave</triggerType>
    </start>
    <status>Active</status>
</Flow>
