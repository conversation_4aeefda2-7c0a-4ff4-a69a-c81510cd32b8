<?xml version="1.0" encoding="UTF-8"?>
<OmniDataTransform xmlns="http://soap.sforce.com/2006/04/metadata">
    <active>true</active>
    <assignmentRulesUsed>false</assignmentRulesUsed>
    <deletedOnSuccess>false</deletedOnSuccess>
    <errorIgnored>false</errorIgnored>
    <expectedInputJson>[ {
  &quot;Ambito&quot; : &quot;&quot;,
  &quot;Esposizione AR&quot; : false,
  &quot;Firmatario&quot; : &quot;&quot;,
  &quot;Frazionamento&quot; : &quot;MENSILE&quot;,
  &quot;<PERSON>lo&quot; : &quot;&quot;,
  &quot;N. Folder&quot; : &quot;101853000212733&quot;,
  &quot;Numero Polizza/Posizione&quot; : [ 192135424 ],
  &quot;Omnicanalità&quot; : &quot;&quot;,
  &quot;Premio&quot; : 0,
  &quot;Rettificabile&quot; : &quot;&quot;,
  &quot;Scadenza&quot; : &quot;2025-05-20T22:00:00&quot;,
  &quot;Società&quot; : &quot;1&quot;,
  &quot;Targa&quot; : &quot;&quot;,
  &quot;Tipo&quot; : &quot;4&quot;,
  &quot;Titoli al legale&quot; : &quot;&quot;,
  &quot;Unibox&quot; : &quot;&quot;,
  &quot;contId&quot; : &quot;922339426762273302&quot;,
  &quot;idContrattoPTF&quot; : &quot;7359394b-c6e5-4779-872a-e819f9533350&quot;,
  &quot;isCompletaAttiva&quot; : false,
  &quot;isFEAAttiva&quot; : false,
  &quot;isFEAVisualizzata&quot; : true,
  &quot;isFirmaAttiva&quot; : true,
  &quot;isIncassaAttiva&quot; : true,
  &quot;isProdottoUnico&quot; : true,
  &quot;isRettificaAttiva&quot; : true,
  &quot;uplCrmQuietId&quot; : &quot;&quot;,
  &quot;xquietanzaId&quot; : &quot;213621734&quot;
} ]</expectedInputJson>
    <fieldLevelSecurityEnabled>false</fieldLevelSecurityEnabled>
    <inputType>JSON</inputType>
    <isManagedUsingStdDesigner>false</isManagedUsingStdDesigner>
    <name>TitoliInScadenzaTransform</name>
    <nullInputsIncludedInOutput>false</nullInputsIncludedInOutput>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>TitoliInScadenzaTransformCustom4753</globalKey>
        <inputFieldName>ramo</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>TitoliInScadenzaTransform</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>ramo</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>TitoliInScadenzaTransformCustom0jI9O000000uIwjUAEItem39</globalKey>
        <inputFieldName>isCompletaAttiva</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>TitoliInScadenzaTransform</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>isCompletaAttiva</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>TitoliInScadenzaTransformCustom0jI9O000000uIwjUAEItem38</globalKey>
        <inputFieldName>idContrattoPTF</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>TitoliInScadenzaTransform</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>idContrattoPTF</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>TitoliInScadenzaTransformCustom0jI9O000000uIwjUAEItem37</globalKey>
        <inputFieldName>Frazionamento</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>TitoliInScadenzaTransform</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>frazionamento</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <transformValuesMappings>{
  &quot;0&quot; : &quot;TEMPORANEO&quot;,
  &quot;1&quot; : &quot;ANNUALE&quot;,
  &quot;2&quot; : &quot;SEMESTRALE&quot;,
  &quot;3&quot; : &quot;QUADRIMESTRALE&quot;,
  &quot;4&quot; : &quot;TRIMESTRALE&quot;,
  &quot;6&quot; : &quot;BIMESTRALE&quot;,
  &quot;8&quot; : &quot;PREMIO UNICO ANTICIPATO&quot;,
  &quot;9&quot; : &quot;MENSILE&quot;
}</transformValuesMappings>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>TitoliInScadenzaTransformCustom0jI9O000000uIwjUAEItem36</globalKey>
        <inputFieldName>firmata</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>TitoliInScadenzaTransform</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>firmata</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>TitoliInScadenzaTransformCustom0jI9O000000uIwjUAEItem27</globalKey>
        <inputFieldName>inviiAr</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>TitoliInScadenzaTransform</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>inviiAr</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>TitoliInScadenzaTransformCustom0jI9O000000uIwjUAEItem26</globalKey>
        <inputFieldName>idDoc</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>TitoliInScadenzaTransform</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>idDoc</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>TitoliInScadenzaTransformCustom0jI9O000000uIwjUAEItem25</globalKey>
        <inputFieldName>Firmatario</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>TitoliInScadenzaTransform</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>firmatario</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>TitoliInScadenzaTransformCustom0jI9O000000uIwjUAEItem24</globalKey>
        <inputFieldName>emessaNonIncassabile</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>TitoliInScadenzaTransform</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>emessaNonIncassabile</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>TitoliInScadenzaTransformCustom0jI9O000000uIwjUAEItem23</globalKey>
        <inputFieldName>dataEffettoTitolo</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>TitoliInScadenzaTransform</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>dataEffettoTitolo</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>TitoliInScadenzaTransformCustom0jI9O000000uIwjUAEItem35</globalKey>
        <inputFieldName>Esposizione AR</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>TitoliInScadenzaTransform</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>esposizioneAR</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>TitoliInScadenzaTransformCustom0jI9O000000uIwjUAEItem34</globalKey>
        <inputFieldName>tipoDocumento</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>TitoliInScadenzaTransform</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>tipoDocumento</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>TitoliInScadenzaTransformCustom0jI9O000000uIwjUAEItem33</globalKey>
        <inputFieldName>Scadenza</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>TitoliInScadenzaTransform</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>Date(dd-MM-yyyy)</outputFieldFormat>
        <outputFieldName>dataScadenzaTitolo</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>TitoliInScadenzaTransformCustom0jI9O000000uIwjUAEItem32</globalKey>
        <inputFieldName>prodottoAbilitatoInvioFea</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>TitoliInScadenzaTransform</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>prodottoAbilitatoInvioFea</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>TitoliInScadenzaTransformCustom0jI9O000000uIwjUAEItem31</globalKey>
        <inputFieldName>operazione</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>TitoliInScadenzaTransform</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>operazione</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>TitoliInScadenzaTransformCustom0jI9O000000uIwjUAEItem30</globalKey>
        <inputFieldName>numeroAppendice</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>TitoliInScadenzaTransform</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>numeroAppendice</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>TitoliInScadenzaTransformCustom0jI9O000000uIwjUAEItem29</globalKey>
        <inputFieldName>flagStampabileInFEA</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>TitoliInScadenzaTransform</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>isStampabileInFEA</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>TitoliInScadenzaTransformCustom0jI9O000000uIwjUAEItem28</globalKey>
        <inputFieldName>inviiRemoti</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>TitoliInScadenzaTransform</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>inviiRemoti</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>TitoliInScadenzaTransformCustom0jI9O000000uIwjUAEItem17</globalKey>
        <inputFieldName>Unibox</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>TitoliInScadenzaTransform</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>unibox</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>TitoliInScadenzaTransformCustom0jI9O000000uIwjUAEItem16</globalKey>
        <inputFieldName>Titoli al legale</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>TitoliInScadenzaTransform</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>titoliAlLegale</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>TitoliInScadenzaTransformCustom0jI9O000000uIwjUAEItem15</globalKey>
        <inputFieldName>Tipo</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>TitoliInScadenzaTransform</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>tipo</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <transformValuesMappings>{
  &quot;0&quot; : &quot;Copertura Provvisoria&quot;,
  &quot;1&quot; : &quot;Nuova Emissione&quot;,
  &quot;2&quot; : &quot;Sostituzione&quot;,
  &quot;3&quot; : &quot;Variazione&quot;,
  &quot;4&quot; : &quot;Quietanza&quot;,
  &quot;5&quot; : &quot;Premio Unico&quot;,
  &quot;6&quot; : &quot;Regolazione Premio&quot;,
  &quot;7&quot; : &quot;Carta Verde&quot;,
  &quot;8&quot; : &quot;Penale&quot;,
  &quot;9&quot; : &quot;Rimborso Premio&quot;
}</transformValuesMappings>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>TitoliInScadenzaTransformCustom0jI9O000000uIwjUAEItem14</globalKey>
        <inputFieldName>Targa</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>TitoliInScadenzaTransform</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>targa</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>TitoliInScadenzaTransformCustom0jI9O000000uIwjUAEItem13</globalKey>
        <inputFieldName>Rettificabile</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>TitoliInScadenzaTransform</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>rettificabile</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>TitoliInScadenzaTransformCustom0jI9O000000uIwjUAEItem12</globalKey>
        <inputFieldName>Premio</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>TitoliInScadenzaTransform</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>premio</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>TitoliInScadenzaTransformCustom0jI9O000000uIwjUAEItem11</globalKey>
        <inputFieldName>Società</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>TitoliInScadenzaTransform</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>compagnia</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <transformValuesMappings>{
  &quot;1&quot; : &quot;Unipol&quot;,
  &quot;4&quot; : &quot;UniSalute&quot;
}</transformValuesMappings>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>TitoliInScadenzaTransformCustom0jI9O000000uIwjUAEItem10</globalKey>
        <inputFieldName>Numero Polizza/Posizione</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>TitoliInScadenzaTransform</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>polizza</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>TitoliInScadenzaTransformCustom0jI9O000000uIwjUAEItem22</globalKey>
        <inputFieldName>contId</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>TitoliInScadenzaTransform</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>contId</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>TitoliInScadenzaTransformCustom0jI9O000000uIwjUAEItem21</globalKey>
        <inputFieldName>agenziaMadre</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>TitoliInScadenzaTransform</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>agenziaMadre</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>TitoliInScadenzaTransformCustom0jI9O000000uIwjUAEItem20</globalKey>
        <inputFieldName>agenzia</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>TitoliInScadenzaTransform</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>agenzia</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>TitoliInScadenzaTransformCustom0jI9O000000uIwjUAEItem19</globalKey>
        <inputFieldName>xquietanzaId</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>TitoliInScadenzaTransform</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>xquietanzaId</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>TitoliInScadenzaTransformCustom0jI9O000000uIwjUAEItem18</globalKey>
        <inputFieldName>uplCrmQuietId</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>TitoliInScadenzaTransform</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>uplCrmQuietId</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>TitoliInScadenzaTransformCustom0jI9O000000uIwjUAEItem1</globalKey>
        <inputFieldName>isFEAAttiva</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>TitoliInScadenzaTransform</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>isFEAAttiva</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>TitoliInScadenzaTransformCustom0jI9O000000uIwjUAEItem0</globalKey>
        <inputFieldName>Ambito</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>TitoliInScadenzaTransform</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>ambito</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>TitoliInScadenzaTransformCustom0jI9O000000uIwjUAEItem9</globalKey>
        <inputFieldName>Omnicanalità</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>TitoliInScadenzaTransform</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>omnicanalita</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>TitoliInScadenzaTransformCustom0jI9O000000uIwjUAEItem8</globalKey>
        <inputFieldName>N. Folder</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>TitoliInScadenzaTransform</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>nFolder</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>TitoliInScadenzaTransformCustom0jI9O000000uIwjUAEItem7</globalKey>
        <inputFieldName>Modello</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>TitoliInScadenzaTransform</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>modello</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>TitoliInScadenzaTransformCustom0jI9O000000uIwjUAEItem6</globalKey>
        <inputFieldName>isRettificaAttiva</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>TitoliInScadenzaTransform</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>isRettificaAttiva</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>TitoliInScadenzaTransformCustom0jI9O000000uIwjUAEItem5</globalKey>
        <inputFieldName>isProdottoUnico</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>TitoliInScadenzaTransform</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>isProdottoUnico</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>TitoliInScadenzaTransformCustom0jI9O000000uIwjUAEItem4</globalKey>
        <inputFieldName>isIncassaAttiva</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>TitoliInScadenzaTransform</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>isIncassaAttiva</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>TitoliInScadenzaTransformCustom0jI9O000000uIwjUAEItem3</globalKey>
        <inputFieldName>isFirmaAttiva</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>TitoliInScadenzaTransform</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>isFirmaAttiva</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>TitoliInScadenzaTransformCustom0jI9O000000uIwjUAEItem2</globalKey>
        <inputFieldName>isFEAVisualizzata</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>TitoliInScadenzaTransform</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>isFEAVisualizzata</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <outputType>JSON</outputType>
    <previewJsonData>[ {
  &quot;Ambito&quot; : &quot;REL&quot;,
  &quot;Esposizione AR&quot; : false,
  &quot;Firmatario&quot; : &quot;&quot;,
  &quot;Frazionamento&quot; : &quot;MENSILE&quot;,
  &quot;Modello&quot; : null,
  &quot;N. Folder&quot; : &quot;101853000120295&quot;,
  &quot;Numero Polizza/Posizione&quot; : [ 192058290, 192058291 ],
  &quot;Omnicanalità&quot; : &quot;&quot;,
  &quot;Premio&quot; : 0,
  &quot;Rettificabile&quot; : &quot;&quot;,
  &quot;Scadenza&quot; : &quot;2024-10-25T22:00:00&quot;,
  &quot;Società&quot; : &quot;1&quot;,
  &quot;Targa&quot; : null,
  &quot;Tipo&quot; : &quot;3&quot;,
  &quot;Titoli al legale&quot; : &quot;&quot;,
  &quot;Unibox&quot; : null,
  &quot;contId&quot; : &quot;639271535797114902&quot;,
  &quot;dataEffettoTitolo&quot; : &quot;2024-10-26&quot;,
  &quot;idContrattoPTF&quot; : &quot;b7a4d568-d614-40e0-9e4b-4ac43fa19eff&quot;,
  &quot;idDoc&quot; : &quot;&quot;,
  &quot;isCompletaAttiva&quot; : false,
  &quot;isFEAAttiva&quot; : false,
  &quot;isFEAVisualizzata&quot; : false,
  &quot;isFirmaAttiva&quot; : true,
  &quot;isIncassaAttiva&quot; : true,
  &quot;isProdottoUnico&quot; : true,
  &quot;isRettificaAttiva&quot; : true,
  &quot;numeroAppendice&quot; : &quot;0&quot;,
  &quot;ramo&quot; : &quot;77&quot;,
  &quot;tipoDocumento&quot; : &quot;Q&quot;,
  &quot;uplCrmQuietId&quot; : &quot;184521142&quot;,
  &quot;xquietanzaId&quot; : &quot;203274538&quot;
} ]</previewJsonData>
    <processSuperBulk>false</processSuperBulk>
    <responseCacheTtlMinutes>0.0</responseCacheTtlMinutes>
    <rollbackOnError>false</rollbackOnError>
    <sourceObject>json</sourceObject>
    <sourceObjectDefault>false</sourceObjectDefault>
    <synchronousProcessThreshold>0.0</synchronousProcessThreshold>
    <type>Transform</type>
    <uniqueName>TitoliInScadenzaTransform_4</uniqueName>
    <versionNumber>1.0</versionNumber>
    <xmlDeclarationRemoved>false</xmlDeclarationRemoved>
</OmniDataTransform>
