/* quoteAccordionDisplay.css */

.custom-card .slds-box.slds-theme_shade {
  /* adatta questi valori come preferisci: */
  padding-left: 0.25rem !important;
  padding-bottom: 0.25rem !important;
}

.custom-card .slds-box.slds-theme_shade {
  padding-left: -10px;
  padding-right: 0 !important;
}

/* Assicura che l’header-row non abbia padding extra */
.custom-card .header-row {
  padding-left: 0 !important;
  padding-bottom: 0 !important;
}

.container-vertical {
    /* Stili del contenitore principale, se presenti */
}

.no-data-container {
    text-align: center;
    padding: 20px;
    font-style: italic;
    color: black;
}

/* *** NUOVO STILE PER IL TEST ULTRACONTENUTO *** */
.quote-header-text {
    display: flex; /* Metti gli span su una riga */
    flex-wrap: wrap; /* Permetti loro di andare a capo su schermi piccoli */
    gap: 15px; /* Spazio tra gli elementi */
    font-size: 10pt; /* Dimensione generale del testo dell'header */
    font-weight: normal; /* Normalizza il peso del font */
    color: black; /* Colore del testo */
    align-items: center; /* Centra verticalmente */
    flex-grow: 1; /* Occupa lo spazio disponibile */
}

.quote-header-item {
    white-space: nowrap; /* Impedisce agli elementi di andare a capo a meno che il flexbox non lo richieda */
}

.quote-header-item strong {
    font-size: 8pt; /* Rendi le etichette un po' più piccole */
    display: block; /* Metti l'etichetta sopra il valore */
    margin-bottom: 2px; /* Piccolo spazio tra etichetta e valore */
}

.quote-name-link {
    color: #3D5E83;
    /* font-size: 12px; Lasciamo che il parent controlli la dimensione per ora */
    text-decoration: none;
}

.quote-name-link:hover {
    text-decoration: underline #006dcc !important;
}

/* Stili per l'intestazione delle coperture (header row) */
.coverage-header-row {
    background-color: #FFFFFF;
    border: 1px solid #FFFFFF;
    padding: var(--lwc-spacingXSmall);
    font-size: 8pt;
    text-align: center;
}

.expand-col {
  /* nessun grow e width fissa piccola */
  flex: 0 0 1.5rem !important;
  max-width: 1.5rem !important;
  padding-left: 0.25rem; /* aggiusta se vuoi spostarlo più vicino */
  padding-right: 0; 
}


/* Stili per le singole righe di dettaglio delle coperture */
.coverage-data-row {
    box-shadow: 1px 2px 5px lightgray;
    border: 1px solid lightgray;
    border-radius: 33px;
    margin: var(--lwc-spacingXxSmall);
    padding: var(--lwc-spacingXxSmall);
    background-color: #FFFFFF;
    align-items: center;
}

.coverage-data-row span {
    font-size: 8pt;
}

/* Rimuovi la rotazione manuale dell'icona, gestita da lightning-accordion */
/* .slds-button_icon-bare .slds-button__icon {
    transition: transform 0.2s ease-in-out;
} */

/* file: quoteAccordionDisplay.css */
.ambiti-cell,
.ambito-detail {
  display: flex;
  justify-content: center;
  align-items: center;
}

.ambiti-cell img {
  width: 30px;
  height: 30px;
}

.ambito-detail span {
  font-size: 0.875rem; /* 14px, oppure quanto vuoi tu */
  font-weight: 600;
}

.no-left-gap .slds-grid {
  margin-left: -0.5rem; /* o quanto ti serve per “tirare” più a sinistra */
  padding-left: 0;
}